# Simple test script for the updated LIDAR processing pipeline
# Tests direct processing of *_raw.txt files with UInt32 photon counts

include("retrieval_program.jl")

println("🧪 TESTING RAW LIDAR FILE PROCESSING")
println("="^50)

# Test file path
test_file = "processed/raw/I2450315.590541_raw.txt"

if isfile(test_file)
    println("✅ Test file found: $test_file")
    
    try
        # Test reading raw file
        println("\n📥 Reading raw LIDAR file...")
        data = read_raw_lidar_file(test_file)
        
        println("✅ File successfully loaded:")
        println("   Filename: $(data.header.filename)")
        println("   Raw counts: $(length(data.raw_counts)) points")
        println("   Photon counts: $(length(data.photon_counts)) points")
        println("   Altitude range: $(minimum(data.altitude):.1f) - $(maximum(data.altitude):.1f) m")
        
        # Test overflow check
        println("\n🛡️ Checking for overflow values...")
        overflow_analysis = check_overflow_values(data.raw_counts)
        
        # Test basic statistics
        println("\n📊 Basic statistics:")
        println("   Min raw count: $(minimum(data.raw_counts))")
        println("   Max raw count: $(maximum(data.raw_counts))")
        println("   Mean photon count: $(mean(data.photon_counts):.2f)")
        println("   Std photon count: $(std(data.photon_counts):.2f)")
        
        # Test CSV export
        println("\n💾 Testing CSV export...")
        mkpath("test_output")
        csv_path = "test_output/test_data.csv"
        save_lidar_data_csv(data, csv_path)
        
        # Test plotting
        println("\n📊 Testing plot generation...")
        plot_path = "test_output/test_plot.png"
        plot_lidar_signal(data, plot_path, " - Test")
        
        # Test noise processing
        println("\n🔧 Testing noise processing...")
        clean_signal, noise_level = process_noise_removal(data, plot_results=false, save_plots=false)
        println("   Noise level: $(noise_level:.2f)")
        println("   Clean signal range: $(minimum(clean_signal):.2f) - $(maximum(clean_signal):.2f)")
        
        println("\n✅ ALL TESTS PASSED!")
        println("Check test_output/ directory for generated files")
        
    catch e
        println("❌ Error during testing: $e")
        println("Stack trace:")
        for (exc, bt) in Base.catch_stack()
            showerror(stdout, exc, bt)
            println()
        end
    end
    
else
    println("❌ Test file not found: $test_file")
    println("Available files in processed/raw/:")
    if isdir("processed/raw")
        files = readdir("processed/raw")
        for file in files[1:min(5, length(files))]
            println("   $file")
        end
        if length(files) > 5
            println("   ... and $(length(files)-5) more files")
        end
    end
end

println("\n" * "="^50)
println("🏁 TEST COMPLETE")
