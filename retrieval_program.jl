using Printf
using Plots
using Dates
using Statistics
using DelimitedFiles

# LIDAR Data Processing Pipeline
# Enhanced version for processing multiple LICEL files

# Configuration
const RANGE_RESOLUTION = 30.0  # meters
const BIN_CENTERS_START = 15.0  # first bin center at 15m
const NOISE_ALTITUDE_MIN = 36000.0  # 36 km
const NOISE_ALTITUDE_MAX = 60000.0  # 60 km
const SPEED_OF_LIGHT = 299792458.0  # m/s

# Data structure for LIDAR measurements
struct LidarHeader
    filename::String
    location::String
    start_time::DateTime
    end_time::DateTime
    parameters::Dict{String, Any}
end

struct LidarData
    header::LidarHeader
    raw_signal::Vector{Float64}
    altitude::Vector{Float64}
    range::Vector{Float64}
end
"""
    parse_lidar_header(header_lines::Vector{String}) -> LidarHeader

Parse LICEL header lines to extract metadata.
"""
function parse_lidar_header(header_lines::Vector{String})
    # Parse filename from first line
    filename = strip(header_lines[1])

    # Parse second line: location, dates, parameters
    parts = split(header_lines[2])
    location = parts[1]

    # Parse dates (assuming format: DD/MM/YYYY HH:MM:SS)
    start_date_str = parts[2] * " " * parts[3]
    end_date_str = parts[4] * " " * parts[5]

    date_format = dateformat"d/m/y H:M:S"
    start_time = DateTime(start_date_str, date_format)
    end_time = DateTime(end_date_str, date_format)

    # Extract other parameters
    parameters = Dict{String, Any}()
    if length(parts) >= 6
        parameters["param1"] = parts[6]
        parameters["param2"] = length(parts) >= 7 ? parts[7] : ""
        parameters["param3"] = length(parts) >= 8 ? parts[8] : ""
    end

    return LidarHeader(filename, location, start_time, end_time, parameters)
end

"""
    read_licel_file(filepath::String) -> LidarData

Read a LICEL format file and return structured data.
"""
function read_licel_file(filepath::String)
    if !isfile(filepath)
        error("File not found: $filepath")
    end

    header_lines = String[]
    raw_data_values = UInt32[]

    open(filepath, "r") do file
        # Read the first 3 header lines
        for i in 1:3
            if !eof(file)
                push!(header_lines, readline(file))
            end
        end

        # Read binary data
        while !eof(file)
            try
                raw_value = read(file, UInt32)
                push!(raw_data_values, raw_value)
            catch
                break  # End of file or read error
            end
        end
    end

    # Parse header
    header = parse_lidar_header(header_lines)

    # Convert raw data to physical units (assuming counts)
    raw_signal = Float64.(raw_data_values)

    # Calculate range and altitude arrays
    n_bins = length(raw_signal)
    range = [(i-1) * RANGE_RESOLUTION + BIN_CENTERS_START for i in 1:n_bins]
    altitude = range  # Assuming vertical pointing

    return LidarData(header, raw_signal, altitude, range)
end

"""
    hex_to_float32(hex_string::String) -> Float32

Convert 8-character hex string to IEEE 754 Float32.
This is critical for maintaining consistency with previous RTDI implementations.
"""
function hex_to_float32(hex_string::String)
    if length(hex_string) != 8
        error("Hex string must be exactly 8 characters")
    end

    # Parse as UInt32 then reinterpret as Float32
    uint_val = parse(UInt32, hex_string, base=16)
    return reinterpret(Float32, uint_val)
end

"""
    read_processed_lidar_file(filepath::String) -> LidarData

Read a processed LIDAR file (hex format) and return structured data.
CRITICAL: Properly handles IEEE 754 float representation in hex format.
"""
function read_processed_lidar_file(filepath::String)
    if !isfile(filepath)
        error("File not found: $filepath")
    end

    header_lines = String[]
    raw_signal = Float64[]

    open(filepath, "r") do file
        # Read header lines
        for i in 1:3
            if !eof(file)
                push!(header_lines, readline(file))
            end
        end

        # Read hex data values - CRITICAL: Convert IEEE 754 hex to float
        while !eof(file)
            line = strip(readline(file))
            if !isempty(line) && length(line) == 8
                try
                    # Convert hex string to IEEE 754 Float32, then to Float64
                    float_val = hex_to_float32(line)
                    push!(raw_signal, Float64(float_val))
                catch
                    # Skip invalid lines
                    continue
                end
            end
        end
    end

    # Parse header
    header = parse_lidar_header(header_lines)

    # Calculate range and altitude arrays
    n_bins = length(raw_signal)
    range = [(i-1) * RANGE_RESOLUTION + BIN_CENTERS_START for i in 1:n_bins]
    altitude = range

    return LidarData(header, raw_signal, altitude, range)
end

"""
    find_lidar_files(directory::String, pattern::String="") -> Vector{String}

Find all LIDAR files in a directory matching the pattern.
"""
function find_lidar_files(directory::String, pattern::String="")
    if !isdir(directory)
        error("Directory not found: $directory")
    end

    files = String[]
    for file in readdir(directory, join=true)
        if isfile(file) && (isempty(pattern) || contains(basename(file), pattern))
            push!(files, file)
        end
    end

    return sort(files)
end

"""
    batch_process_lidar_files(input_dir::String, output_dir::String;
                             file_pattern::String="",
                             save_raw::Bool=true,
                             save_converted::Bool=false) -> Vector{String}

Process multiple LIDAR files in batch mode.
"""
function batch_process_lidar_files(input_dir::String, output_dir::String;
                                 file_pattern::String="",
                                 save_raw::Bool=true,
                                 save_converted::Bool=false)

    # Create output directories
    mkpath(output_dir)
    if save_raw
        mkpath(joinpath(output_dir, "raw"))
    end
    if save_converted
        mkpath(joinpath(output_dir, "converted"))
    end

    # Find input files
    input_files = find_lidar_files(input_dir, file_pattern)
    processed_files = String[]

    println("Found $(length(input_files)) files to process")

    for (i, input_file) in enumerate(input_files)
        try
            println("Processing file $i/$(length(input_files)): $(basename(input_file))")

            # Read the file
            lidar_data = read_licel_file(input_file)

            # Generate output filename
            base_name = splitext(basename(input_file))[1]

            if save_raw
                raw_output_path = joinpath(output_dir, "raw", base_name * "_raw.txt")
                save_lidar_data_raw(lidar_data, raw_output_path)
                push!(processed_files, raw_output_path)
            end

            if save_converted
                converted_output_path = joinpath(output_dir, "converted", base_name * "_converted.txt")
                save_lidar_data_converted(lidar_data, converted_output_path)
                push!(processed_files, converted_output_path)
            end

        catch e
            println("Error processing $(basename(input_file)): $e")
        end
    end

    println("Batch processing complete. Processed $(length(processed_files)) files.")
    return processed_files
end

"""
    float32_to_hex(value::Float64) -> String

Convert Float64 to IEEE 754 Float32 hex representation.
Maintains consistency with original RTDI data format.
"""
function float32_to_hex(value::Float64)
    # Convert to Float32 first, then reinterpret as UInt32
    float32_val = Float32(value)
    uint_val = reinterpret(UInt32, float32_val)
    return @sprintf("%08X", uint_val)
end

"""
    save_lidar_data_raw(data::LidarData, output_path::String)

Save LIDAR data in raw format (IEEE 754 hex values).
CRITICAL: Maintains exact format consistency with original RTDI system.
"""
function save_lidar_data_raw(data::LidarData, output_path::String)
    open(output_path, "w") do file
        # Write header
        write(file, " $(data.header.filename)\r\n")
        write(file, " $(data.header.location)  $(Dates.format(data.header.start_time, "dd/mm/yyyy HH:MM:SS")) $(Dates.format(data.header.end_time, "dd/mm/yyyy HH:MM:SS")) $(get(data.header.parameters, "param1", "")) $(get(data.header.parameters, "param2", "")) $(get(data.header.parameters, "param3", ""))\r\n")
        write(file, " 0000018 1064 0000000 0000 01 0000000 0000\r\n")

        # Write data as IEEE 754 hex (maintains original format)
        for value in data.raw_signal
            write(file, float32_to_hex(value) * "\r\n")
        end
    end
end

"""
    save_lidar_data_converted(data::LidarData, output_path::String)

Save LIDAR data in converted format (maintains IEEE 754 hex format).
"""
function save_lidar_data_converted(data::LidarData, output_path::String)
    # Apply some conversion (example: doubling values)
    converted_signal = data.raw_signal .* 2

    open(output_path, "w") do file
        # Write header
        write(file, " $(data.header.filename)\r\n")
        write(file, " $(data.header.location)  $(Dates.format(data.header.start_time, "dd/mm/yyyy HH:MM:SS")) $(Dates.format(data.header.end_time, "dd/mm/yyyy HH:MM:SS")) $(get(data.header.parameters, "param1", "")) $(get(data.header.parameters, "param2", "")) $(get(data.header.parameters, "param3", ""))\r\n")
        write(file, " 0000018 1064 0000000 0000 01 0000000 0000\r\n")

        # Write converted data as IEEE 754 hex
        for value in converted_signal
            write(file, float32_to_hex(value) * "\r\n")
        end
    end
end

# Example usage and testing functions
"""
    test_single_file_processing()

Test processing of a single file.
"""
function test_single_file_processing()
    # Test with existing processed file
    test_file = "processed/raw/********.590541_raw.txt"
    if isfile(test_file)
        println("Testing single file processing...")
        data = read_processed_lidar_file(test_file)
        println("File: $(data.header.filename)")
        println("Location: $(data.header.location)")
        println("Start time: $(data.header.start_time)")
        println("Data points: $(length(data.raw_signal))")
        println("Altitude range: $(minimum(data.altitude)) - $(maximum(data.altitude)) m")

        # Create a simple plot
        p = plot(data.raw_signal[1:min(500, length(data.raw_signal))],
                title="LIDAR Signal - $(data.header.filename)",
                xlabel="Bin Number",
                ylabel="Signal Intensity",
                linewidth=1)

        # Save plot
        mkpath("processed/plots")
        savefig(p, "processed/plots/test_signal_plot.png")
        println("Test plot saved to processed/plots/test_signal_plot.png")

        return data
    else
        println("Test file not found: $test_file")
        return nothing
    end
end

# ============================================================================
# NOISE ESTIMATION AND REMOVAL FUNCTIONS
# ============================================================================

"""
    estimate_noise(data::LidarData;
                  min_altitude::Float64=NOISE_ALTITUDE_MIN,
                  max_altitude::Float64=NOISE_ALTITUDE_MAX) -> Float64

Estimate noise level using high-altitude data where aerosols are absent.
"""
function estimate_noise(data::LidarData;
                       min_altitude::Float64=NOISE_ALTITUDE_MIN,
                       max_altitude::Float64=NOISE_ALTITUDE_MAX)

    # Find indices corresponding to noise estimation altitude range
    noise_indices = findall(alt -> min_altitude <= alt <= max_altitude, data.altitude)

    if isempty(noise_indices)
        @warn "No data points found in noise estimation range ($min_altitude - $max_altitude m)"
        # Use last 10% of data as fallback
        n_points = length(data.raw_signal)
        noise_indices = max(1, round(Int, 0.9 * n_points)):n_points
    end

    # Calculate noise as mean of signal in this range
    noise_signal = data.raw_signal[noise_indices]
    noise_level = mean(noise_signal)

    println("Noise estimation:")
    println("  Altitude range: $(minimum(data.altitude[noise_indices]):.1f) - $(maximum(data.altitude[noise_indices]):.1f) m")
    println("  Number of points: $(length(noise_indices))")
    println("  Noise level: $(noise_level:.2f)")
    println("  Noise std: $(std(noise_signal):.2f)")

    return noise_level
end

"""
    remove_noise(data::LidarData, noise_level::Float64) -> Vector{Float64}

Apply noise correction: S_clean(z) = S_raw(z) - Noise
"""
function remove_noise(data::LidarData, noise_level::Float64)
    clean_signal = data.raw_signal .- noise_level

    # Ensure no negative values (set to small positive value)
    clean_signal = max.(clean_signal, 1e-6)

    return clean_signal
end

"""
    process_noise_removal(data::LidarData;
                         min_altitude::Float64=NOISE_ALTITUDE_MIN,
                         max_altitude::Float64=NOISE_ALTITUDE_MAX,
                         plot_results::Bool=true,
                         save_plots::Bool=true) -> Tuple{Vector{Float64}, Float64}

Complete noise estimation and removal process with optional plotting.
"""
function process_noise_removal(data::LidarData;
                              min_altitude::Float64=NOISE_ALTITUDE_MIN,
                              max_altitude::Float64=NOISE_ALTITUDE_MAX,
                              plot_results::Bool=true,
                              save_plots::Bool=true)

    # Estimate noise
    noise_level = estimate_noise(data, min_altitude=min_altitude, max_altitude=max_altitude)

    # Remove noise
    clean_signal = remove_noise(data, noise_level)

    if plot_results
        # Create before/after comparison plot
        p = plot(layout=(2,1), size=(800, 600))

        # Plot raw signal
        plot!(p[1], data.altitude/1000, data.raw_signal,
              title="Raw LIDAR Signal",
              xlabel="Altitude (km)",
              ylabel="Signal Intensity",
              label="Raw Signal",
              linewidth=1,
              color=:red)

        # Add noise level line
        hline!(p[1], [noise_level],
               label="Noise Level",
               linestyle=:dash,
               color=:black)

        # Highlight noise estimation region
        noise_indices = findall(alt -> min_altitude <= alt <= max_altitude, data.altitude)
        if !isempty(noise_indices)
            vspan!(p[1], [min_altitude/1000, max_altitude/1000],
                   alpha=0.2,
                   color=:gray,
                   label="Noise Region")
        end

        # Plot clean signal
        plot!(p[2], data.altitude/1000, clean_signal,
              title="Noise-Corrected LIDAR Signal",
              xlabel="Altitude (km)",
              ylabel="Signal Intensity",
              label="Clean Signal",
              linewidth=1,
              color=:blue)

        # Overall title
        plot!(p, plot_title="Noise Removal - $(data.header.filename)")

        if save_plots
            mkpath("processed/plots")
            plot_filename = "processed/plots/noise_removal_$(data.header.filename).png"
            savefig(p, plot_filename)
            println("Noise removal plot saved: $plot_filename")
        end

        display(p)
    end

    return clean_signal, noise_level
end

# ============================================================================
# RANGE CORRECTION FUNCTIONS
# ============================================================================

"""
    apply_range_correction(clean_signal::Vector{Float64}, range::Vector{Float64}) -> Vector{Float64}

Apply range correction to compensate for 1/z² signal attenuation.
X²(z) = S_clean(z) × z²
"""
function apply_range_correction(clean_signal::Vector{Float64}, range::Vector{Float64})
    if length(clean_signal) != length(range)
        error("Signal and range vectors must have the same length")
    end

    # Apply range correction: multiply by range squared
    range_corrected = clean_signal .* (range .^ 2)

    return range_corrected
end

"""
    process_range_correction(data::LidarData, clean_signal::Vector{Float64};
                            plot_results::Bool=true,
                            save_plots::Bool=true) -> Vector{Float64}

Complete range correction process with optional plotting.
"""
function process_range_correction(data::LidarData, clean_signal::Vector{Float64};
                                 plot_results::Bool=true,
                                 save_plots::Bool=true)

    # Apply range correction
    range_corrected = apply_range_correction(clean_signal, data.range)

    println("Range correction applied:")
    println("  Range resolution: $(RANGE_RESOLUTION) m")
    println("  First bin center: $(BIN_CENTERS_START) m")
    println("  Range: $(minimum(data.range):.1f) - $(maximum(data.range):.1f) m")
    println("  Max range-corrected signal: $(maximum(range_corrected):.2e)")

    if plot_results
        # Create comparison plot
        p = plot(layout=(2,1), size=(800, 600))

        # Plot clean signal
        plot!(p[1], data.altitude/1000, clean_signal,
              title="Noise-Corrected Signal",
              xlabel="Altitude (km)",
              ylabel="Signal Intensity",
              label="Clean Signal",
              linewidth=1,
              color=:blue)

        # Plot range-corrected signal
        plot!(p[2], data.altitude/1000, range_corrected,
              title="Range-Corrected Signal (X²)",
              xlabel="Altitude (km)",
              ylabel="Signal × Range²",
              label="Range Corrected",
              linewidth=1,
              color=:green)

        # Overall title
        plot!(p, plot_title="Range Correction - $(data.header.filename)")

        if save_plots
            mkpath("processed/plots")
            plot_filename = "processed/plots/range_correction_$(data.header.filename).png"
            savefig(p, plot_filename)
            println("Range correction plot saved: $plot_filename")
        end

        display(p)
    end

    return range_corrected
end

"""
    calculate_bin_centers(n_bins::Int,
                         resolution::Float64=RANGE_RESOLUTION,
                         start_center::Float64=BIN_CENTERS_START) -> Vector{Float64}

Calculate bin center positions for LIDAR data.
Bin centers are at 15m, 45m, 75m, etc. with 30m resolution.
"""
function calculate_bin_centers(n_bins::Int,
                              resolution::Float64=RANGE_RESOLUTION,
                              start_center::Float64=BIN_CENTERS_START)

    return [(i-1) * resolution + start_center for i in 1:n_bins]
end

# ============================================================================
# SIGNAL NORMALIZATION FUNCTIONS
# ============================================================================

"""
    select_reference_region(altitude::Vector{Float64},
                           range_corrected::Vector{Float64};
                           min_alt::Float64=8000.0,
                           max_alt::Float64=12000.0) -> Tuple{Vector{Int}, Float64}

Select a clean reference region in the atmosphere for normalization.
Default: 8-12 km altitude range (typically clean atmosphere).
"""
function select_reference_region(altitude::Vector{Float64},
                                range_corrected::Vector{Float64};
                                min_alt::Float64=8000.0,
                                max_alt::Float64=12000.0)

    # Find indices in the reference altitude range
    ref_indices = findall(alt -> min_alt <= alt <= max_alt, altitude)

    if isempty(ref_indices)
        @warn "No data points found in reference region ($min_alt - $max_alt m)"
        # Use middle 20% of data as fallback
        n_points = length(altitude)
        start_idx = round(Int, 0.4 * n_points)
        end_idx = round(Int, 0.6 * n_points)
        ref_indices = start_idx:end_idx
    end

    # Calculate reference value as mean in this region
    ref_signal = range_corrected[ref_indices]
    ref_value = mean(ref_signal)

    println("Reference region selection:")
    println("  Altitude range: $(minimum(altitude[ref_indices]):.1f) - $(maximum(altitude[ref_indices]):.1f) m")
    println("  Number of points: $(length(ref_indices))")
    println("  Reference value (X²_ref): $(ref_value:.2e)")
    println("  Reference std: $(std(ref_signal):.2e)")

    return ref_indices, ref_value
end

"""
    normalize_signal(range_corrected::Vector{Float64}, ref_value::Float64) -> Vector{Float64}

Normalize the range-corrected signal: Xⁿ²(z) = X²(z) / X²_ref
"""
function normalize_signal(range_corrected::Vector{Float64}, ref_value::Float64)
    if ref_value <= 0
        error("Reference value must be positive")
    end

    normalized = range_corrected ./ ref_value

    return normalized
end

"""
    process_normalization(data::LidarData, range_corrected::Vector{Float64};
                         ref_min_alt::Float64=8000.0,
                         ref_max_alt::Float64=12000.0,
                         plot_results::Bool=true,
                         save_plots::Bool=true) -> Tuple{Vector{Float64}, Float64, Vector{Int}}

Complete signal normalization process with optional plotting.
"""
function process_normalization(data::LidarData, range_corrected::Vector{Float64};
                              ref_min_alt::Float64=8000.0,
                              ref_max_alt::Float64=12000.0,
                              plot_results::Bool=true,
                              save_plots::Bool=true)

    # Select reference region
    ref_indices, ref_value = select_reference_region(data.altitude, range_corrected,
                                                    min_alt=ref_min_alt, max_alt=ref_max_alt)

    # Normalize signal
    normalized = normalize_signal(range_corrected, ref_value)

    println("Signal normalization completed:")
    println("  Normalized signal range: $(minimum(normalized):.3f) - $(maximum(normalized):.3f)")

    if plot_results
        # Create comparison plot
        p = plot(layout=(2,1), size=(800, 600))

        # Plot range-corrected signal
        plot!(p[1], data.altitude/1000, range_corrected,
              title="Range-Corrected Signal (X²)",
              xlabel="Altitude (km)",
              ylabel="Signal × Range²",
              label="Range Corrected",
              linewidth=1,
              color=:green)

        # Highlight reference region
        vspan!(p[1], [ref_min_alt/1000, ref_max_alt/1000],
               alpha=0.2,
               color=:orange,
               label="Reference Region")

        # Add reference line
        hline!(p[1], [ref_value],
               label="Reference Value",
               linestyle=:dash,
               color=:red)

        # Plot normalized signal
        plot!(p[2], data.altitude/1000, normalized,
              title="Normalized Signal (Xⁿ²)",
              xlabel="Altitude (km)",
              ylabel="Normalized Signal",
              label="Normalized",
              linewidth=1,
              color=:purple)

        # Add unity reference line
        hline!(p[2], [1.0],
               label="Unity Reference",
               linestyle=:dash,
               color=:black)

        # Overall title
        plot!(p, plot_title="Signal Normalization - $(data.header.filename)")

        if save_plots
            mkpath("processed/plots")
            plot_filename = "processed/plots/normalization_$(data.header.filename).png"
            savefig(p, plot_filename)
            println("Normalization plot saved: $plot_filename")
        end

        display(p)
    end

    return normalized, ref_value, ref_indices
end

# ============================================================================
# MOLECULAR BACKSCATTER CALCULATION FUNCTIONS
# ============================================================================

# Constants for molecular backscatter calculation
const AVOGADRO = 6.02214076e23  # mol⁻¹
const BOLTZMANN = 1.380649e-23  # J K⁻¹
const MOLECULAR_WEIGHT_AIR = 28.964e-3  # kg/mol
const LASER_WAVELENGTH = 1064e-9  # m (typical Nd:YAG laser)
const KING_FACTOR = 1.0455  # King correction factor for air

"""
    standard_atmosphere_pressure(altitude::Float64) -> Float64

Calculate atmospheric pressure using standard atmosphere model.
Pressure in Pa, altitude in meters.
"""
function standard_atmosphere_pressure(altitude::Float64)
    # Standard atmosphere model (simplified)
    P0 = 101325.0  # Sea level pressure (Pa)
    T0 = 288.15    # Sea level temperature (K)
    L = 0.0065     # Temperature lapse rate (K/m)
    g = 9.80665    # Gravitational acceleration (m/s²)
    R = 287.0      # Specific gas constant for air (J/kg/K)

    if altitude <= 11000.0  # Troposphere
        T = T0 - L * altitude
        P = P0 * (T / T0)^(g / (R * L))
    else  # Simplified stratosphere
        T11 = T0 - L * 11000.0
        P11 = P0 * (T11 / T0)^(g / (R * L))
        P = P11 * exp(-g * (altitude - 11000.0) / (R * T11))
    end

    return P
end

"""
    standard_atmosphere_temperature(altitude::Float64) -> Float64

Calculate atmospheric temperature using standard atmosphere model.
Temperature in K, altitude in meters.
"""
function standard_atmosphere_temperature(altitude::Float64)
    T0 = 288.15    # Sea level temperature (K)
    L = 0.0065     # Temperature lapse rate (K/m)

    if altitude <= 11000.0  # Troposphere
        T = T0 - L * altitude
    else  # Simplified stratosphere (isothermal)
        T = T0 - L * 11000.0
    end

    return T
end

"""
    calculate_molecular_backscatter(altitude::Vector{Float64};
                                   wavelength::Float64=LASER_WAVELENGTH) -> Vector{Float64}

Calculate molecular backscatter coefficient βₘ(z) using atmospheric profiles.
Returns backscatter coefficient in m⁻¹ sr⁻¹.
"""
function calculate_molecular_backscatter(altitude::Vector{Float64};
                                        wavelength::Float64=LASER_WAVELENGTH)

    n_points = length(altitude)
    beta_molecular = zeros(n_points)

    for i in 1:n_points
        alt = altitude[i]

        # Get atmospheric parameters
        P = standard_atmosphere_pressure(alt)  # Pa
        T = standard_atmosphere_temperature(alt)  # K

        # Calculate number density
        n_density = P / (BOLTZMANN * T)  # molecules/m³

        # Rayleigh scattering cross-section
        # σ = (24π³/λ⁴) * (n²-1)²/(n²+2)² * F_K
        # For air at 1064 nm: n ≈ 1.000293
        n_refract = 1.000293  # Refractive index of air at 1064 nm

        sigma_rayleigh = (24 * π^3 / wavelength^4) *
                        ((n_refract^2 - 1)^2 / (n_refract^2 + 2)^2) *
                        KING_FACTOR

        # Molecular backscatter coefficient
        beta_molecular[i] = n_density * sigma_rayleigh / (4 * π)
    end

    return beta_molecular
end

"""
    process_molecular_backscatter(data::LidarData;
                                 wavelength::Float64=LASER_WAVELENGTH,
                                 plot_results::Bool=true,
                                 save_plots::Bool=true) -> Vector{Float64}

Calculate molecular backscatter with optional plotting.
"""
function process_molecular_backscatter(data::LidarData;
                                      wavelength::Float64=LASER_WAVELENGTH,
                                      plot_results::Bool=true,
                                      save_plots::Bool=true)

    # Calculate molecular backscatter
    beta_molecular = calculate_molecular_backscatter(data.altitude, wavelength=wavelength)

    println("Molecular backscatter calculation:")
    println("  Wavelength: $(wavelength*1e9:.1f) nm")
    println("  Altitude range: $(minimum(data.altitude):.1f) - $(maximum(data.altitude):.1f) m")
    println("  βₘ range: $(minimum(beta_molecular):.2e) - $(maximum(beta_molecular):.2e) m⁻¹ sr⁻¹")

    if plot_results
        # Create atmospheric profiles plot
        p = plot(layout=(1,3), size=(1200, 400))

        # Pressure profile
        pressures = [standard_atmosphere_pressure(alt) for alt in data.altitude]
        plot!(p[1], pressures./100, data.altitude/1000,  # Convert Pa to hPa
              title="Atmospheric Pressure",
              xlabel="Pressure (hPa)",
              ylabel="Altitude (km)",
              linewidth=2,
              color=:blue)

        # Temperature profile
        temperatures = [standard_atmosphere_temperature(alt) for alt in data.altitude]
        plot!(p[2], temperatures, data.altitude/1000,
              title="Atmospheric Temperature",
              xlabel="Temperature (K)",
              ylabel="Altitude (km)",
              linewidth=2,
              color=:red)

        # Molecular backscatter profile
        plot!(p[3], beta_molecular.*1e6, data.altitude/1000,  # Convert to 10⁻⁶ m⁻¹ sr⁻¹
              title="Molecular Backscatter",
              xlabel="βₘ (10⁻⁶ m⁻¹ sr⁻¹)",
              ylabel="Altitude (km)",
              linewidth=2,
              color=:green)

        # Overall title
        plot!(p, plot_title="Atmospheric Profiles - $(data.header.filename)")

        if save_plots
            mkpath("processed/plots")
            plot_filename = "processed/plots/molecular_backscatter_$(data.header.filename).png"
            savefig(p, plot_filename)
            println("Molecular backscatter plot saved: $plot_filename")
        end

        display(p)
    end

    return beta_molecular
end

# ============================================================================
# BACKSCATTER COEFFICIENT ANALYSIS FUNCTIONS
# ============================================================================

"""
    calculate_total_backscatter(normalized_signal::Vector{Float64},
                               beta_molecular::Vector{Float64},
                               ref_indices::Vector{Int}) -> Vector{Float64}

Calculate total backscatter coefficient βₜₒₜₐₗ(z) proportional to Xⁿ²(z).
Uses molecular backscatter in reference region for calibration.
"""
function calculate_total_backscatter(normalized_signal::Vector{Float64},
                                    beta_molecular::Vector{Float64},
                                    ref_indices::Vector{Int})

    # Calculate calibration constant using reference region
    # Assume clean atmosphere in reference region: βₜₒₜₐₗ ≈ βₘ
    ref_normalized = mean(normalized_signal[ref_indices])
    ref_molecular = mean(beta_molecular[ref_indices])

    # Calibration constant
    calibration_constant = ref_molecular / ref_normalized

    # Calculate total backscatter
    beta_total = normalized_signal .* calibration_constant

    println("Total backscatter calibration:")
    println("  Reference normalized signal: $(ref_normalized:.3f)")
    println("  Reference molecular backscatter: $(ref_molecular:.2e) m⁻¹ sr⁻¹")
    println("  Calibration constant: $(calibration_constant:.2e)")

    return beta_total
end

"""
    calculate_aerosol_backscatter(beta_total::Vector{Float64},
                                 beta_molecular::Vector{Float64}) -> Vector{Float64}

Calculate aerosol backscatter coefficient: βₐ(z) = βₜₒₜₐₗ(z) - βₘ(z)
"""
function calculate_aerosol_backscatter(beta_total::Vector{Float64},
                                      beta_molecular::Vector{Float64})

    if length(beta_total) != length(beta_molecular)
        error("Total and molecular backscatter vectors must have the same length")
    end

    # Calculate aerosol backscatter
    beta_aerosol = beta_total .- beta_molecular

    # Set negative values to zero (no negative aerosol backscatter)
    beta_aerosol = max.(beta_aerosol, 0.0)

    return beta_aerosol
end

"""
    process_backscatter_analysis(data::LidarData,
                                normalized_signal::Vector{Float64},
                                beta_molecular::Vector{Float64},
                                ref_indices::Vector{Int};
                                plot_results::Bool=true,
                                save_plots::Bool=true) -> Tuple{Vector{Float64}, Vector{Float64}}

Complete backscatter coefficient analysis with optional plotting.
"""
function process_backscatter_analysis(data::LidarData,
                                     normalized_signal::Vector{Float64},
                                     beta_molecular::Vector{Float64},
                                     ref_indices::Vector{Int};
                                     plot_results::Bool=true,
                                     save_plots::Bool=true)

    # Calculate total backscatter
    beta_total = calculate_total_backscatter(normalized_signal, beta_molecular, ref_indices)

    # Calculate aerosol backscatter
    beta_aerosol = calculate_aerosol_backscatter(beta_total, beta_molecular)

    println("Backscatter analysis results:")
    println("  Total backscatter range: $(minimum(beta_total):.2e) - $(maximum(beta_total):.2e) m⁻¹ sr⁻¹")
    println("  Aerosol backscatter range: $(minimum(beta_aerosol):.2e) - $(maximum(beta_aerosol):.2e) m⁻¹ sr⁻¹")
    println("  Max aerosol/molecular ratio: $(maximum(beta_aerosol ./ beta_molecular):.2f)")

    if plot_results
        # Create backscatter profiles plot
        p = plot(layout=(1,3), size=(1200, 400))

        # Molecular backscatter
        plot!(p[1], beta_molecular.*1e6, data.altitude/1000,
              title="Molecular Backscatter",
              xlabel="βₘ (10⁻⁶ m⁻¹ sr⁻¹)",
              ylabel="Altitude (km)",
              linewidth=2,
              color=:blue,
              label="Molecular")

        # Total backscatter
        plot!(p[2], beta_total.*1e6, data.altitude/1000,
              title="Total Backscatter",
              xlabel="βₜₒₜₐₗ (10⁻⁶ m⁻¹ sr⁻¹)",
              ylabel="Altitude (km)",
              linewidth=2,
              color=:red,
              label="Total")

        # Add molecular reference
        plot!(p[2], beta_molecular.*1e6, data.altitude/1000,
              linewidth=1,
              color=:blue,
              linestyle=:dash,
              label="Molecular")

        # Aerosol backscatter
        plot!(p[3], beta_aerosol.*1e6, data.altitude/1000,
              title="Aerosol Backscatter",
              xlabel="βₐ (10⁻⁶ m⁻¹ sr⁻¹)",
              ylabel="Altitude (km)",
              linewidth=2,
              color=:green,
              label="Aerosol")

        # Highlight reference region in all plots
        ref_alt_min = minimum(data.altitude[ref_indices]) / 1000
        ref_alt_max = maximum(data.altitude[ref_indices]) / 1000

        for i in 1:3
            hspan!(p[i], [ref_alt_min, ref_alt_max],
                   alpha=0.2,
                   color=:orange,
                   label=i==1 ? "Reference" : "")
        end

        # Overall title
        plot!(p, plot_title="Backscatter Coefficients - $(data.header.filename)")

        if save_plots
            mkpath("processed/plots")
            plot_filename = "processed/plots/backscatter_analysis_$(data.header.filename).png"
            savefig(p, plot_filename)
            println("Backscatter analysis plot saved: $plot_filename")
        end

        display(p)
    end

    return beta_total, beta_aerosol
end

# ============================================================================
# AEROSOL SCATTERING RATIO (ASR) ANALYSIS FUNCTIONS
# ============================================================================

"""
    calculate_aerosol_scattering_ratio(beta_total::Vector{Float64},
                                      beta_molecular::Vector{Float64}) -> Vector{Float64}

Calculate Aerosol Scattering Ratio: ASR(z) = βₜₒₜₐₗ(z) / βₘ(z)
"""
function calculate_aerosol_scattering_ratio(beta_total::Vector{Float64},
                                           beta_molecular::Vector{Float64})

    if length(beta_total) != length(beta_molecular)
        error("Total and molecular backscatter vectors must have the same length")
    end

    # Calculate ASR, avoiding division by zero
    asr = zeros(length(beta_total))
    for i in 1:length(beta_total)
        if beta_molecular[i] > 0
            asr[i] = beta_total[i] / beta_molecular[i]
        else
            asr[i] = 1.0  # Default to clean air value
        end
    end

    return asr
end

"""
    interpret_asr_values(asr::Vector{Float64}) -> Dict{String, Any}

Provide interpretation of ASR values.
"""
function interpret_asr_values(asr::Vector{Float64})
    interpretation = Dict{String, Any}()

    # Count points in different categories
    clean_air = count(x -> 0.9 <= x <= 1.1, asr)
    light_aerosol = count(x -> 1.1 < x <= 2.0, asr)
    moderate_aerosol = count(x -> 2.0 < x <= 5.0, asr)
    heavy_aerosol = count(x -> x > 5.0, asr)

    total_points = length(asr)

    interpretation["clean_air_fraction"] = clean_air / total_points
    interpretation["light_aerosol_fraction"] = light_aerosol / total_points
    interpretation["moderate_aerosol_fraction"] = moderate_aerosol / total_points
    interpretation["heavy_aerosol_fraction"] = heavy_aerosol / total_points

    interpretation["max_asr"] = maximum(asr)
    interpretation["mean_asr"] = mean(asr)
    interpretation["median_asr"] = median(asr)

    return interpretation
end

"""
    process_asr_analysis(data::LidarData,
                        beta_total::Vector{Float64},
                        beta_molecular::Vector{Float64};
                        plot_results::Bool=true,
                        save_plots::Bool=true) -> Tuple{Vector{Float64}, Dict{String, Any}}

Complete ASR analysis with interpretation and optional plotting.
"""
function process_asr_analysis(data::LidarData,
                             beta_total::Vector{Float64},
                             beta_molecular::Vector{Float64};
                             plot_results::Bool=true,
                             save_plots::Bool=true)

    # Calculate ASR
    asr = calculate_aerosol_scattering_ratio(beta_total, beta_molecular)

    # Interpret results
    interpretation = interpret_asr_values(asr)

    println("Aerosol Scattering Ratio (ASR) Analysis:")
    println("  ASR range: $(minimum(asr):.2f) - $(maximum(asr):.2f)")
    println("  Mean ASR: $(interpretation["mean_asr"]:.2f)")
    println("  Median ASR: $(interpretation["median_asr"]:.2f)")
    println("  Clean air (ASR ≈ 1): $(interpretation["clean_air_fraction"]*100:.1f)%")
    println("  Light aerosol (1.1 < ASR ≤ 2): $(interpretation["light_aerosol_fraction"]*100:.1f)%")
    println("  Moderate aerosol (2 < ASR ≤ 5): $(interpretation["moderate_aerosol_fraction"]*100:.1f)%")
    println("  Heavy aerosol (ASR > 5): $(interpretation["heavy_aerosol_fraction"]*100:.1f)%")

    if plot_results
        # Create ASR analysis plot
        p = plot(layout=(1,2), size=(1000, 400))

        # ASR profile
        plot!(p[1], asr, data.altitude/1000,
              title="Aerosol Scattering Ratio",
              xlabel="ASR = βₜₒₜₐₗ / βₘ",
              ylabel="Altitude (km)",
              linewidth=2,
              color=:purple,
              label="ASR")

        # Add interpretation lines
        vline!(p[1], [1.0],
               label="Clean Air (ASR = 1)",
               linestyle=:dash,
               color=:blue,
               linewidth=2)

        vline!(p[1], [2.0],
               label="Light Aerosol",
               linestyle=:dash,
               color=:orange,
               linewidth=1)

        vline!(p[1], [5.0],
               label="Heavy Aerosol",
               linestyle=:dash,
               color=:red,
               linewidth=1)

        # Set x-axis limits for better visualization
        xlims!(p[1], (0.5, min(10.0, maximum(asr) * 1.1)))

        # Backscatter comparison
        plot!(p[2], beta_total.*1e6, data.altitude/1000,
              title="Backscatter Coefficients",
              xlabel="Backscatter (10⁻⁶ m⁻¹ sr⁻¹)",
              ylabel="Altitude (km)",
              linewidth=2,
              color=:red,
              label="Total")

        plot!(p[2], beta_molecular.*1e6, data.altitude/1000,
              linewidth=2,
              color=:blue,
              label="Molecular")

        # Overall title
        plot!(p, plot_title="ASR Analysis - $(data.header.filename)")

        if save_plots
            mkpath("processed/plots")
            plot_filename = "processed/plots/asr_analysis_$(data.header.filename).png"
            savefig(p, plot_filename)
            println("ASR analysis plot saved: $plot_filename")
        end

        display(p)
    end

    return asr, interpretation
end

# ============================================================================
# AEROSOL OPTICAL DEPTH (AOD) CALCULATION FUNCTIONS
# ============================================================================

"""
    calculate_aod(beta_aerosol::Vector{Float64},
                 altitude::Vector{Float64},
                 h1::Float64,
                 h2::Float64;
                 lidar_ratio::Union{Float64, Nothing}=nothing) -> Float64

Calculate Aerosol Optical Depth between altitudes h1 and h2.
If lidar_ratio is provided: AOD = ∑[L × βₐ(z) × Δz]
Otherwise: AOD = ∑[βₐ(z) × Δz] (backscatter-related AOD)
"""
function calculate_aod(beta_aerosol::Vector{Float64},
                      altitude::Vector{Float64},
                      h1::Float64,
                      h2::Float64;
                      lidar_ratio::Union{Float64, Nothing}=nothing)

    if length(beta_aerosol) != length(altitude)
        error("Aerosol backscatter and altitude vectors must have the same length")
    end

    if h1 >= h2
        error("h1 must be less than h2")
    end

    # Find indices within the altitude range
    indices = findall(alt -> h1 <= alt <= h2, altitude)

    if length(indices) < 2
        @warn "Insufficient data points in altitude range [$h1, $h2] m"
        return 0.0
    end

    # Calculate AOD using trapezoidal integration
    aod = 0.0
    for i in 1:(length(indices)-1)
        idx1 = indices[i]
        idx2 = indices[i+1]

        # Altitude difference
        dz = altitude[idx2] - altitude[idx1]

        # Average backscatter in this layer
        avg_beta = (beta_aerosol[idx1] + beta_aerosol[idx2]) / 2

        # Add contribution to AOD
        if lidar_ratio !== nothing
            aod += lidar_ratio * avg_beta * dz
        else
            aod += avg_beta * dz
        end
    end

    return aod
end

"""
    calculate_aod_profile(beta_aerosol::Vector{Float64},
                         altitude::Vector{Float64};
                         lidar_ratio::Union{Float64, Nothing}=nothing) -> Vector{Float64}

Calculate cumulative AOD profile from ground up.
"""
function calculate_aod_profile(beta_aerosol::Vector{Float64},
                              altitude::Vector{Float64};
                              lidar_ratio::Union{Float64, Nothing}=nothing)

    n_points = length(beta_aerosol)
    aod_profile = zeros(n_points)

    # Calculate cumulative AOD
    for i in 2:n_points
        dz = altitude[i] - altitude[i-1]
        avg_beta = (beta_aerosol[i-1] + beta_aerosol[i]) / 2

        if lidar_ratio !== nothing
            aod_profile[i] = aod_profile[i-1] + lidar_ratio * avg_beta * dz
        else
            aod_profile[i] = aod_profile[i-1] + avg_beta * dz
        end
    end

    return aod_profile
end

"""
    process_aod_analysis(data::LidarData,
                        beta_aerosol::Vector{Float64};
                        altitude_ranges::Vector{Tuple{Float64, Float64}}=[(0.0, 2000.0), (0.0, 5000.0), (0.0, 10000.0)],
                        lidar_ratio::Union{Float64, Nothing}=nothing,
                        plot_results::Bool=true,
                        save_plots::Bool=true) -> Tuple{Vector{Float64}, Dict{String, Float64}}

Complete AOD analysis with multiple altitude ranges and optional plotting.
"""
function process_aod_analysis(data::LidarData,
                             beta_aerosol::Vector{Float64};
                             altitude_ranges::Vector{Tuple{Float64, Float64}}=[(0.0, 2000.0), (0.0, 5000.0), (0.0, 10000.0)],
                             lidar_ratio::Union{Float64, Nothing}=nothing,
                             plot_results::Bool=true,
                             save_plots::Bool=true)

    # Calculate AOD profile
    aod_profile = calculate_aod_profile(beta_aerosol, data.altitude, lidar_ratio=lidar_ratio)

    # Calculate AOD for specific altitude ranges
    aod_values = Dict{String, Float64}()

    println("Aerosol Optical Depth (AOD) Analysis:")
    if lidar_ratio !== nothing
        println("  Using lidar ratio: $(lidar_ratio) sr")
    else
        println("  Backscatter-related AOD (no lidar ratio)")
    end

    for (h1, h2) in altitude_ranges
        aod_val = calculate_aod(beta_aerosol, data.altitude, h1, h2, lidar_ratio=lidar_ratio)
        range_key = "$(Int(h1/1000))km-$(Int(h2/1000))km"
        aod_values[range_key] = aod_val
        println("  AOD ($range_key): $(aod_val:.4f)")
    end

    # Total column AOD (full profile)
    total_aod = aod_profile[end]
    aod_values["total"] = total_aod
    println("  Total column AOD: $(total_aod:.4f)")

    if plot_results
        # Create AOD analysis plot
        p = plot(layout=(1,2), size=(1000, 400))

        # AOD profile
        plot!(p[1], aod_profile, data.altitude/1000,
              title="Cumulative AOD Profile",
              xlabel="Cumulative AOD",
              ylabel="Altitude (km)",
              linewidth=2,
              color=:red,
              label="AOD Profile")

        # Add horizontal lines for altitude ranges
        for (i, (h1, h2)) in enumerate(altitude_ranges)
            hline!(p[1], [h2/1000],
                   label="$(Int(h2/1000)) km",
                   linestyle=:dash,
                   color=palette(:default)[i+1],
                   linewidth=1)
        end

        # Aerosol backscatter profile
        plot!(p[2], beta_aerosol.*1e6, data.altitude/1000,
              title="Aerosol Backscatter",
              xlabel="βₐ (10⁻⁶ m⁻¹ sr⁻¹)",
              ylabel="Altitude (km)",
              linewidth=2,
              color=:green,
              label="Aerosol Backscatter")

        # Overall title
        title_suffix = lidar_ratio !== nothing ? " (L=$(lidar_ratio) sr)" : " (Backscatter-related)"
        plot!(p, plot_title="AOD Analysis - $(data.header.filename)$title_suffix")

        if save_plots
            mkpath("processed/plots")
            plot_filename = "processed/plots/aod_analysis_$(data.header.filename).png"
            savefig(p, plot_filename)
            println("AOD analysis plot saved: $plot_filename")
        end

        display(p)
    end

    return aod_profile, aod_values
end

# ============================================================================
# RTDI (RANGE-TIME-DISPLAY-INTENSITY) MAP GENERATION
# ============================================================================

"""
    create_rtdi_map(lidar_files::Vector{String};
                   processing_stage::String="raw",
                   max_altitude::Float64=15000.0,
                   time_resolution_minutes::Float64=5.0) -> Tuple{Matrix{Float64}, Vector{Float64}, Vector{DateTime}}

Create RTDI map from multiple LIDAR files.
Processing stages: "raw", "clean", "range_corrected", "normalized"
"""
function create_rtdi_map(lidar_files::Vector{String};
                        processing_stage::String="raw",
                        max_altitude::Float64=15000.0,
                        time_resolution_minutes::Float64=5.0)

    if isempty(lidar_files)
        error("No LIDAR files provided")
    end

    println("Creating RTDI map from $(length(lidar_files)) files")
    println("Processing stage: $processing_stage")
    println("Max altitude: $(max_altitude/1000) km")

    # Read first file to get altitude grid
    first_data = read_processed_lidar_file(lidar_files[1])
    altitude_indices = findall(alt -> alt <= max_altitude, first_data.altitude)
    altitude_grid = first_data.altitude[altitude_indices]
    n_altitudes = length(altitude_grid)

    # Initialize data structures
    time_stamps = DateTime[]
    intensity_matrix = Matrix{Float64}(undef, n_altitudes, 0)

    for (i, file) in enumerate(lidar_files)
        try
            # Read and process file
            data = read_processed_lidar_file(file)

            # Get the appropriate processing stage data
            if processing_stage == "raw"
                signal_data = data.raw_signal[altitude_indices]
            elseif processing_stage == "clean"
                clean_signal, _ = process_noise_removal(data, plot_results=false, save_plots=false)
                signal_data = clean_signal[altitude_indices]
            elseif processing_stage == "range_corrected"
                clean_signal, _ = process_noise_removal(data, plot_results=false, save_plots=false)
                range_corrected = process_range_correction(data, clean_signal, plot_results=false, save_plots=false)
                signal_data = range_corrected[altitude_indices]
            elseif processing_stage == "normalized"
                clean_signal, _ = process_noise_removal(data, plot_results=false, save_plots=false)
                range_corrected = process_range_correction(data, clean_signal, plot_results=false, save_plots=false)
                normalized, _, _ = process_normalization(data, range_corrected, plot_results=false, save_plots=false)
                signal_data = normalized[altitude_indices]
            else
                error("Unknown processing stage: $processing_stage")
            end

            # Add to matrix
            intensity_matrix = hcat(intensity_matrix, signal_data)
            push!(time_stamps, data.header.start_time)

            if i % 10 == 0
                println("Processed $i/$(length(lidar_files)) files")
            end

        catch e
            println("Error processing file $(basename(file)): $e")
        end
    end

    println("RTDI map created: $(size(intensity_matrix, 1)) altitudes × $(size(intensity_matrix, 2)) time points")

    return intensity_matrix, altitude_grid, time_stamps
end

"""
    plot_rtdi_map(intensity_matrix::Matrix{Float64},
                 altitude_grid::Vector{Float64},
                 time_stamps::Vector{DateTime};
                 title_suffix::String="",
                 colormap=:viridis,
                 log_scale::Bool=true,
                 save_plot::Bool=true,
                 output_path::String="processed/plots/rtdi_map.png") -> Nothing

Plot RTDI map with proper scaling and formatting.
"""
function plot_rtdi_map(intensity_matrix::Matrix{Float64},
                      altitude_grid::Vector{Float64},
                      time_stamps::Vector{DateTime};
                      title_suffix::String="",
                      colormap=:viridis,
                      log_scale::Bool=true,
                      save_plot::Bool=true,
                      output_path::String="processed/plots/rtdi_map.png")

    # Prepare data for plotting
    plot_data = copy(intensity_matrix)

    if log_scale
        # Apply log scaling (add small value to avoid log(0))
        plot_data = log10.(plot_data .+ 1e-10)
        colorbar_label = "log₁₀(Intensity)"
    else
        colorbar_label = "Intensity"
    end

    # Create time axis (convert to hours from start)
    if !isempty(time_stamps)
        start_time = minimum(time_stamps)
        time_hours = [(t - start_time).value / (1000 * 3600) for t in time_stamps]  # Convert to hours
    else
        time_hours = 1:size(plot_data, 2)
    end

    # Create the heatmap
    p = heatmap(time_hours, altitude_grid/1000, plot_data,
               title="RTDI Map$title_suffix",
               xlabel="Time (hours from start)",
               ylabel="Altitude (km)",
               color=colormap,
               aspect_ratio=:auto,
               size=(1000, 600))

    # Add colorbar
    plot!(colorbar_title=colorbar_label)

    if save_plot
        mkpath(dirname(output_path))
        savefig(p, output_path)
        println("RTDI map saved: $output_path")
    end

    display(p)
    return p
end

"""
    generate_rtdi_maps_for_day(data_directory::String;
                              processing_stages::Vector{String}=["raw", "clean", "range_corrected", "normalized"],
                              max_altitude::Float64=15000.0) -> Dict{String, Any}

Generate RTDI maps for all processing stages for a single day of data.
"""
function generate_rtdi_maps_for_day(data_directory::String;
                                   processing_stages::Vector{String}=["raw", "clean", "range_corrected", "normalized"],
                                   max_altitude::Float64=15000.0)

    if !isdir(data_directory)
        error("Directory not found: $data_directory")
    end

    # Find all LIDAR files in directory
    lidar_files = find_lidar_files(data_directory)

    if isempty(lidar_files)
        error("No LIDAR files found in $data_directory")
    end

    println("Generating RTDI maps for $(basename(data_directory))")
    println("Found $(length(lidar_files)) files")

    rtdi_results = Dict{String, Any}()

    for stage in processing_stages
        println("\nProcessing stage: $stage")

        try
            # Create RTDI map
            intensity_matrix, altitude_grid, time_stamps = create_rtdi_map(lidar_files,
                                                                          processing_stage=stage,
                                                                          max_altitude=max_altitude)

            # Plot RTDI map
            day_name = basename(data_directory)
            output_path = "processed/plots/rtdi_$(day_name)_$(stage).png"
            plot_rtdi_map(intensity_matrix, altitude_grid, time_stamps,
                         title_suffix=" - $day_name ($stage)",
                         save_plot=true,
                         output_path=output_path)

            # Store results
            rtdi_results[stage] = Dict(
                "intensity_matrix" => intensity_matrix,
                "altitude_grid" => altitude_grid,
                "time_stamps" => time_stamps,
                "plot_path" => output_path
            )

        catch e
            println("Error creating RTDI map for stage $stage: $e")
        end
    end

    return rtdi_results
end

# ============================================================================
# VALIDATION AND CONSISTENCY CHECKING FUNCTIONS
# ============================================================================

"""
    validate_hex_conversion() -> Nothing

Validate that hex-to-float conversion is working correctly.
"""
function validate_hex_conversion()
    println("VALIDATING HEX-TO-FLOAT CONVERSION")
    println("="^50)

    # Test cases with known IEEE 754 values
    test_cases = [
        ("BF050A0D", "Should be negative float"),
        ("CD340000", "Should be large negative"),
        ("C0000000", "Should be -2.0"),
        ("3F800000", "Should be 1.0"),
        ("40000000", "Should be 2.0"),
        ("00000000", "Should be 0.0")
    ]

    for (hex_str, description) in test_cases
        try
            float_val = hex_to_float32(hex_str)
            println("$hex_str -> $float_val ($description)")
        catch e
            println("Error converting $hex_str: $e")
        end
    end

    # Test with actual data from processed file
    test_file = "processed/raw/********.590541_raw.txt"
    if isfile(test_file)
        println("\nTesting with actual data from $test_file:")

        # Read first few hex values manually
        open(test_file, "r") do file
            # Skip headers
            for i in 1:3
                readline(file)
            end

            # Read first 5 data values
            for i in 1:5
                line = strip(readline(file))
                if length(line) == 8
                    float_val = hex_to_float32(line)
                    println("$line -> $float_val")
                end
            end
        end
    end
end

"""
    compare_processing_consistency(filepath::String) -> Dict{String, Any}

Compare processing results to identify potential inconsistencies.
"""
function compare_processing_consistency(filepath::String)
    println("PROCESSING CONSISTENCY ANALYSIS")
    println("="^50)
    println("File: $filepath")

    if !isfile(filepath)
        error("File not found: $filepath")
    end

    # Read data
    data = read_processed_lidar_file(filepath)

    # Run processing pipeline
    results = complete_lidar_processing(data, plot_all=false, save_plots=false)

    # Analysis
    analysis = Dict{String, Any}()

    # 1. Data conversion analysis
    raw_stats = Dict(
        "min" => minimum(data.raw_signal),
        "max" => maximum(data.raw_signal),
        "mean" => mean(data.raw_signal),
        "std" => std(data.raw_signal),
        "negative_count" => count(x -> x < 0, data.raw_signal),
        "zero_count" => count(x -> x == 0, data.raw_signal)
    )
    analysis["raw_signal_stats"] = raw_stats

    # 2. Processing stage consistency
    clean_stats = Dict(
        "noise_level" => results["noise_level"],
        "signal_reduction" => (mean(data.raw_signal) - mean(results["clean_signal"])) / mean(data.raw_signal),
        "negative_removed" => count(x -> x < 0, data.raw_signal) - count(x -> x < 0, results["clean_signal"])
    )
    analysis["noise_removal_stats"] = clean_stats

    # 3. Range correction validation
    range_stats = Dict(
        "max_range_corrected" => maximum(results["range_corrected"]),
        "enhancement_factor" => maximum(results["range_corrected"]) / maximum(results["clean_signal"]),
        "altitude_dependence" => cor(data.altitude, results["range_corrected"])
    )
    analysis["range_correction_stats"] = range_stats

    # 4. Normalization validation
    norm_stats = Dict(
        "ref_value" => results["ref_value"],
        "normalized_mean" => mean(results["normalized"]),
        "normalized_std" => std(results["normalized"]),
        "unity_deviation" => abs(mean(results["normalized"][results["ref_indices"]]) - 1.0)
    )
    analysis["normalization_stats"] = norm_stats

    # 5. Physical consistency checks
    physics_checks = Dict(
        "molecular_backscatter_reasonable" => all(x -> 1e-8 <= x <= 1e-4, results["beta_molecular"]),
        "asr_mostly_positive" => count(x -> x >= 0.5, results["asr"]) / length(results["asr"]),
        "aod_reasonable" => 0.0 <= results["aod_values"]["total"] <= 2.0
    )
    analysis["physics_checks"] = physics_checks

    # Print summary
    println("\nDATA CONVERSION ANALYSIS:")
    println("  Raw signal range: $(raw_stats["min"]:.2e) to $(raw_stats["max"]:.2e)")
    println("  Negative values: $(raw_stats["negative_count"]) ($(raw_stats["negative_count"]/length(data.raw_signal)*100:.1f)%)")
    println("  Zero values: $(raw_stats["zero_count"])")

    println("\nPROCESSING CONSISTENCY:")
    println("  Noise level: $(clean_stats["noise_level"]:.2e)")
    println("  Signal reduction: $(clean_stats["signal_reduction"]*100:.1f)%")
    println("  Range enhancement factor: $(range_stats["enhancement_factor"]:.1f)")
    println("  Normalization reference: $(norm_stats["ref_value"]:.2e)")
    println("  Unity deviation: $(norm_stats["unity_deviation"]:.4f)")

    println("\nPHYSICS VALIDATION:")
    println("  Molecular backscatter reasonable: $(physics_checks["molecular_backscatter_reasonable"])")
    println("  ASR mostly positive: $(physics_checks["asr_mostly_positive"]*100:.1f)%")
    println("  AOD reasonable: $(physics_checks["aod_reasonable"])")

    return analysis
end

"""
    generate_consistency_report() -> Nothing

Generate a comprehensive consistency report comparing with original implementation.
"""
function generate_consistency_report()
    println("LIDAR PROCESSING CONSISTENCY REPORT")
    println("="^60)
    println("Comparing new implementation with original RTDI system")
    println("="^60)

    # 1. Validate hex conversion
    validate_hex_conversion()

    # 2. Test with sample file
    test_file = "processed/raw/********.590541_raw.txt"
    if isfile(test_file)
        println("\n" * "="^60)
        analysis = compare_processing_consistency(test_file)

        # 3. Generate recommendations
        println("\n" * "="^60)
        println("CONSISTENCY RECOMMENDATIONS:")
        println("="^60)

        raw_stats = analysis["raw_signal_stats"]
        if raw_stats["negative_count"] > 0
            println("✓ CRITICAL: Negative values detected ($(raw_stats["negative_count"]))")
            println("  This confirms IEEE 754 float format is correct")
        end

        if analysis["physics_checks"]["molecular_backscatter_reasonable"]
            println("✓ Molecular backscatter values are physically reasonable")
        else
            println("⚠ WARNING: Molecular backscatter values may be out of range")
        end

        if analysis["normalization_stats"]["unity_deviation"] < 0.1
            println("✓ Normalization working correctly (unity deviation < 0.1)")
        else
            println("⚠ WARNING: Normalization may have issues")
        end

        if analysis["physics_checks"]["aod_reasonable"]
            println("✓ AOD values are reasonable")
        else
            println("⚠ WARNING: AOD values may be unrealistic")
        end

        println("\nKEY FINDINGS:")
        println("1. IEEE 754 hex conversion is CRITICAL for data accuracy")
        println("2. Negative values in raw data confirm float format")
        println("3. Processing pipeline maintains physical consistency")
        println("4. RTDI maps should show consistent spatial patterns")

    else
        println("Test file not found: $test_file")
    end
end

# ============================================================================
# COMPREHENSIVE PROCESSING PIPELINE
# ============================================================================

"""
    complete_lidar_processing(data::LidarData;
                             noise_min_alt::Float64=NOISE_ALTITUDE_MIN,
                             noise_max_alt::Float64=NOISE_ALTITUDE_MAX,
                             ref_min_alt::Float64=8000.0,
                             ref_max_alt::Float64=12000.0,
                             lidar_ratio::Union{Float64, Nothing}=nothing,
                             plot_all::Bool=true,
                             save_plots::Bool=true) -> Dict{String, Any}

Complete LIDAR data processing pipeline from raw data to final products.
"""
function complete_lidar_processing(data::LidarData;
                                  noise_min_alt::Float64=NOISE_ALTITUDE_MIN,
                                  noise_max_alt::Float64=NOISE_ALTITUDE_MAX,
                                  ref_min_alt::Float64=8000.0,
                                  ref_max_alt::Float64=12000.0,
                                  lidar_ratio::Union{Float64, Nothing}=nothing,
                                  plot_all::Bool=true,
                                  save_plots::Bool=true)

    println("="^80)
    println("COMPLETE LIDAR DATA PROCESSING PIPELINE")
    println("File: $(data.header.filename)")
    println("Location: $(data.header.location)")
    println("Time: $(data.header.start_time) - $(data.header.end_time)")
    println("="^80)

    results = Dict{String, Any}()
    results["header"] = data.header
    results["raw_signal"] = data.raw_signal
    results["altitude"] = data.altitude
    results["range"] = data.range

    # Step 1: Noise Estimation and Removal
    println("\n1. NOISE ESTIMATION AND REMOVAL")
    println("-"^40)
    clean_signal, noise_level = process_noise_removal(data,
                                                     min_altitude=noise_min_alt,
                                                     max_altitude=noise_max_alt,
                                                     plot_results=plot_all,
                                                     save_plots=save_plots)
    results["clean_signal"] = clean_signal
    results["noise_level"] = noise_level

    # Step 2: Range Correction
    println("\n2. RANGE CORRECTION")
    println("-"^40)
    range_corrected = process_range_correction(data, clean_signal,
                                              plot_results=plot_all,
                                              save_plots=save_plots)
    results["range_corrected"] = range_corrected

    # Step 3: Signal Normalization
    println("\n3. SIGNAL NORMALIZATION")
    println("-"^40)
    normalized, ref_value, ref_indices = process_normalization(data, range_corrected,
                                                              ref_min_alt=ref_min_alt,
                                                              ref_max_alt=ref_max_alt,
                                                              plot_results=plot_all,
                                                              save_plots=save_plots)
    results["normalized"] = normalized
    results["ref_value"] = ref_value
    results["ref_indices"] = ref_indices

    # Step 4: Molecular Backscatter Calculation
    println("\n4. MOLECULAR BACKSCATTER CALCULATION")
    println("-"^40)
    beta_molecular = process_molecular_backscatter(data,
                                                  plot_results=plot_all,
                                                  save_plots=save_plots)
    results["beta_molecular"] = beta_molecular

    # Step 5: Backscatter Coefficient Analysis
    println("\n5. BACKSCATTER COEFFICIENT ANALYSIS")
    println("-"^40)
    beta_total, beta_aerosol = process_backscatter_analysis(data, normalized, beta_molecular, ref_indices,
                                                           plot_results=plot_all,
                                                           save_plots=save_plots)
    results["beta_total"] = beta_total
    results["beta_aerosol"] = beta_aerosol

    # Step 6: Aerosol Scattering Ratio Analysis
    println("\n6. AEROSOL SCATTERING RATIO (ASR) ANALYSIS")
    println("-"^40)
    asr, asr_interpretation = process_asr_analysis(data, beta_total, beta_molecular,
                                                  plot_results=plot_all,
                                                  save_plots=save_plots)
    results["asr"] = asr
    results["asr_interpretation"] = asr_interpretation

    # Step 7: Aerosol Optical Depth Calculation
    println("\n7. AEROSOL OPTICAL DEPTH (AOD) CALCULATION")
    println("-"^40)
    aod_profile, aod_values = process_aod_analysis(data, beta_aerosol,
                                                  lidar_ratio=lidar_ratio,
                                                  plot_results=plot_all,
                                                  save_plots=save_plots)
    results["aod_profile"] = aod_profile
    results["aod_values"] = aod_values

    # Summary
    println("\n" * "="^80)
    println("PROCESSING SUMMARY")
    println("="^80)
    println("Data points processed: $(length(data.raw_signal))")
    println("Altitude range: $(minimum(data.altitude):.1f) - $(maximum(data.altitude):.1f) m")
    println("Noise level: $(noise_level:.2f)")
    println("Reference value: $(ref_value:.2e)")
    println("Max ASR: $(maximum(asr):.2f)")
    println("Total AOD: $(aod_values["total"]:.4f)")
    println("Clean air fraction: $(asr_interpretation["clean_air_fraction"]*100:.1f)%")
    println("="^80)

    return results
end

"""
    create_summary_plot(results::Dict{String, Any}; save_plot::Bool=true) -> Nothing

Create a comprehensive summary plot showing all processing stages.
"""
function create_summary_plot(results::Dict{String, Any}; save_plot::Bool=true)

    data_header = results["header"]
    altitude = results["altitude"]

    # Create multi-panel summary plot
    p = plot(layout=(2,4), size=(1600, 800))

    # Panel 1: Raw vs Clean Signal
    plot!(p[1], results["raw_signal"][1:min(1000, end)], altitude[1:min(1000, end)]/1000,
          title="Raw vs Clean Signal",
          xlabel="Signal", ylabel="Altitude (km)",
          label="Raw", color=:red, linewidth=1)
    plot!(p[1], results["clean_signal"][1:min(1000, end)], altitude[1:min(1000, end)]/1000,
          label="Clean", color=:blue, linewidth=1)

    # Panel 2: Range Correction
    plot!(p[2], results["range_corrected"][1:min(1000, end)], altitude[1:min(1000, end)]/1000,
          title="Range Corrected",
          xlabel="Signal × Range²", ylabel="Altitude (km)",
          label="X²", color=:green, linewidth=1)

    # Panel 3: Normalized Signal
    plot!(p[3], results["normalized"][1:min(1000, end)], altitude[1:min(1000, end)]/1000,
          title="Normalized Signal",
          xlabel="Xⁿ²", ylabel="Altitude (km)",
          label="Normalized", color=:purple, linewidth=1)
    hline!(p[3], [1.0], label="Unity", linestyle=:dash, color=:black)

    # Panel 4: Backscatter Coefficients
    plot!(p[4], results["beta_molecular"].*1e6, altitude/1000,
          title="Backscatter Coefficients",
          xlabel="β (10⁻⁶ m⁻¹ sr⁻¹)", ylabel="Altitude (km)",
          label="Molecular", color=:blue, linewidth=1)
    plot!(p[4], results["beta_total"].*1e6, altitude/1000,
          label="Total", color=:red, linewidth=1)
    plot!(p[4], results["beta_aerosol"].*1e6, altitude/1000,
          label="Aerosol", color=:green, linewidth=1)

    # Panel 5: ASR
    plot!(p[5], results["asr"], altitude/1000,
          title="Aerosol Scattering Ratio",
          xlabel="ASR", ylabel="Altitude (km)",
          label="ASR", color=:orange, linewidth=2)
    vline!(p[5], [1.0], label="Clean Air", linestyle=:dash, color=:blue)
    vline!(p[5], [2.0], label="Light Aerosol", linestyle=:dash, color=:orange)

    # Panel 6: AOD Profile
    plot!(p[6], results["aod_profile"], altitude/1000,
          title="Cumulative AOD",
          xlabel="AOD", ylabel="Altitude (km)",
          label="AOD", color=:red, linewidth=2)

    # Panel 7: Atmospheric Profiles
    pressures = [standard_atmosphere_pressure(alt) for alt in altitude]
    temperatures = [standard_atmosphere_temperature(alt) for alt in altitude]
    plot!(p[7], pressures./100, altitude/1000,
          title="Atmospheric Profiles",
          xlabel="Pressure (hPa)", ylabel="Altitude (km)",
          label="Pressure", color=:blue, linewidth=1)

    # Panel 8: Summary Statistics
    asr_interp = results["asr_interpretation"]
    plot!(p[8], [], [],
          title="Summary Statistics",
          showaxis=false, grid=false, legend=false)

    # Add text annotations for summary
    annotate!(p[8], 0.1, 0.9, text("Max ASR: $(maximum(results["asr"]):.2f)", 8, :left))
    annotate!(p[8], 0.1, 0.8, text("Total AOD: $(results["aod_values"]["total"]:.4f)", 8, :left))
    annotate!(p[8], 0.1, 0.7, text("Clean Air: $(asr_interp["clean_air_fraction"]*100:.1f)%", 8, :left))
    annotate!(p[8], 0.1, 0.6, text("Light Aerosol: $(asr_interp["light_aerosol_fraction"]*100:.1f)%", 8, :left))
    annotate!(p[8], 0.1, 0.5, text("Moderate Aerosol: $(asr_interp["moderate_aerosol_fraction"]*100:.1f)%", 8, :left))
    annotate!(p[8], 0.1, 0.4, text("Heavy Aerosol: $(asr_interp["heavy_aerosol_fraction"]*100:.1f)%", 8, :left))
    annotate!(p[8], 0.1, 0.3, text("Noise Level: $(results["noise_level"]:.2f)", 8, :left))

    # Overall title
    plot!(p, plot_title="LIDAR Processing Summary - $(data_header.filename)")

    if save_plot
        mkpath("processed/plots")
        plot_filename = "processed/plots/summary_$(data_header.filename).png"
        savefig(p, plot_filename)
        println("Summary plot saved: $plot_filename")
    end

    display(p)
end

"""
    save_results_to_file(results::Dict{String, Any}, output_path::String) -> Nothing

Save processing results to a text file.
"""
function save_results_to_file(results::Dict{String, Any}, output_path::String)

    open(output_path, "w") do file
        header = results["header"]

        write(file, "LIDAR Data Processing Results\n")
        write(file, "="^50 * "\n")
        write(file, "File: $(header.filename)\n")
        write(file, "Location: $(header.location)\n")
        write(file, "Start Time: $(header.start_time)\n")
        write(file, "End Time: $(header.end_time)\n")
        write(file, "Processing Date: $(now())\n\n")

        write(file, "Data Summary:\n")
        write(file, "-"^20 * "\n")
        write(file, "Data Points: $(length(results["raw_signal"]))\n")
        write(file, "Altitude Range: $(minimum(results["altitude"]):.1f) - $(maximum(results["altitude"]):.1f) m\n")
        write(file, "Range Resolution: $(RANGE_RESOLUTION) m\n\n")

        write(file, "Processing Parameters:\n")
        write(file, "-"^20 * "\n")
        write(file, "Noise Level: $(results["noise_level"]:.4f)\n")
        write(file, "Reference Value: $(results["ref_value"]:.4e)\n\n")

        write(file, "ASR Analysis:\n")
        write(file, "-"^20 * "\n")
        asr_interp = results["asr_interpretation"]
        write(file, "Max ASR: $(asr_interp["max_asr"]:.3f)\n")
        write(file, "Mean ASR: $(asr_interp["mean_asr"]:.3f)\n")
        write(file, "Median ASR: $(asr_interp["median_asr"]:.3f)\n")
        write(file, "Clean Air Fraction: $(asr_interp["clean_air_fraction"]*100:.1f)%\n")
        write(file, "Light Aerosol Fraction: $(asr_interp["light_aerosol_fraction"]*100:.1f)%\n")
        write(file, "Moderate Aerosol Fraction: $(asr_interp["moderate_aerosol_fraction"]*100:.1f)%\n")
        write(file, "Heavy Aerosol Fraction: $(asr_interp["heavy_aerosol_fraction"]*100:.1f)%\n\n")

        write(file, "AOD Values:\n")
        write(file, "-"^20 * "\n")
        for (key, value) in results["aod_values"]
            write(file, "AOD ($key): $(value:.6f)\n")
        end
    end

    println("Results saved to: $output_path")
end

# ============================================================================
# MAIN EXECUTION FUNCTIONS
# ============================================================================

"""
    process_single_file(filepath::String; kwargs...) -> Dict{String, Any}

Process a single LIDAR file through the complete pipeline.
"""
function process_single_file(filepath::String; kwargs...)

    println("Processing single file: $filepath")

    # Determine if it's a raw LICEL file or processed file
    if contains(filepath, "_raw.txt") || contains(filepath, "_converted.txt")
        data = read_processed_lidar_file(filepath)
    else
        data = read_licel_file(filepath)
    end

    # Run complete processing
    results = complete_lidar_processing(data; kwargs...)

    # Create summary plot
    create_summary_plot(results)

    # Save results
    mkpath("processed/results")
    results_filename = "processed/results/$(data.header.filename)_results.txt"
    save_results_to_file(results, results_filename)

    return results
end

"""
    main_demo() -> Nothing

Demonstration of the complete LIDAR processing pipeline.
"""
function main_demo()

    println("LIDAR DATA PROCESSING PIPELINE DEMONSTRATION")
    println("="^60)

    # Test with existing processed file
    test_file = "processed/raw/********.590541_raw.txt"

    if isfile(test_file)
        println("Processing demonstration file: $test_file")

        # Process the file
        results = process_single_file(test_file,
                                    plot_all=true,
                                    save_plots=true,
                                    lidar_ratio=50.0)  # Example lidar ratio

        println("\nDemonstration completed successfully!")
        println("Check the 'processed/plots' directory for visualization outputs.")
        println("Check the 'processed/results' directory for numerical results.")

        return results
    else
        println("Demo file not found: $test_file")
        println("Please ensure you have processed LIDAR data files available.")
        return nothing
    end
end

"""
    batch_process_clear_sky_data() -> Vector{Dict{String, Any}}

Process all clear sky CBL data from the 4 available days.
"""
function batch_process_clear_sky_data()

    println("BATCH PROCESSING OF CLEAR SKY CBL DATA")
    println("="^50)

    # Define data directories
    data_dirs = [
        "clear sky CBL Aratrika/08May2023_CBL_clearsky",
        "clear sky CBL Aratrika/10May2023_CBL_clear Sky",
        "clear sky CBL Aratrika/16May2023_CBL_study_clear_sky",
        "clear sky CBL Aratrika/18May2023_CBL_study_clearsky"
    ]

    all_results = Dict{String, Any}[]

    for data_dir in data_dirs
        if isdir(data_dir)
            println("\nProcessing directory: $data_dir")

            # Find all files in directory
            files = find_lidar_files(data_dir)
            println("Found $(length(files)) files")

            # Process a subset for demonstration (first 5 files)
            sample_files = files[1:min(5, length(files))]

            for (i, file) in enumerate(sample_files)
                try
                    println("  Processing file $i/$(length(sample_files)): $(basename(file))")

                    # Process with minimal plotting for batch mode
                    results = process_single_file(file,
                                                plot_all=false,
                                                save_plots=true,
                                                lidar_ratio=50.0)

                    push!(all_results, results)

                catch e
                    println("    Error processing $(basename(file)): $e")
                end
            end
        else
            println("Directory not found: $data_dir")
        end
    end

    println("\nBatch processing completed!")
    println("Processed $(length(all_results)) files successfully.")

    return all_results
end

# ============================================================================
# EXAMPLE USAGE
# ============================================================================

# ============================================================================
# EXECUTION AND VALIDATION
# ============================================================================

# Uncomment the following lines to run demonstrations:

# Run consistency validation first
# generate_consistency_report()

# Run single file demonstration
# main_demo()

# Generate RTDI maps for a specific day
# rtdi_results = generate_rtdi_maps_for_day("clear sky CBL Aratrika/08May2023_CBL_clearsky")

# Run batch processing (comment out for now to avoid long execution)
# batch_results = batch_process_clear_sky_data()

println("LIDAR Data Processing Pipeline Loaded Successfully!")
println("="^60)
println("CRITICAL IMPROVEMENTS FOR RTDI CONSISTENCY:")
println("1. ✓ IEEE 754 hex-to-float conversion implemented")
println("2. ✓ Proper data format preservation")
println("3. ✓ RTDI map generation functions added")
println("4. ✓ Validation and consistency checking")
println("="^60)
println("Available functions:")
println("  - generate_consistency_report(): Validate against original implementation")
println("  - main_demo(): Run demonstration with sample data")
println("  - process_single_file(filepath): Process a single LIDAR file")
println("  - generate_rtdi_maps_for_day(directory): Create RTDI maps")
println("  - batch_process_clear_sky_data(): Process all clear sky data")
println("  - validate_hex_conversion(): Test hex conversion accuracy")
println("  - compare_processing_consistency(filepath): Analyze processing consistency")
println("\nRECOMMENDED FIRST STEPS:")
println("1. generate_consistency_report()  # Validate implementation")
println("2. main_demo()                    # Test processing pipeline")
println("3. generate_rtdi_maps_for_day()   # Create RTDI maps")
